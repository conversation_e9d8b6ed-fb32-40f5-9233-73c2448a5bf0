using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;
using ZombieGame.UI;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Handles all input related to inventory management
    /// </summary>
    public class InventoryInputController : MonoBehaviour
    {
        [Header("Input Settings")]
        [SerializeField] private KeyCode inventoryToggleKey = KeyCode.Tab;
        [SerializeField] private KeyCode dropItemKey = KeyCode.Q;
        [SerializeField] private KeyCode useItemKey = KeyCode.Mouse0; // Left mouse button
        
        [Header("Hotbar Keys")]
        [SerializeField] private KeyCode[] hotbarKeys = {
            KeyCode.Alpha1, KeyCode.Alpha2, KeyCode.Alpha3, KeyCode.Alpha4,
            KeyCode.Alpha5, KeyCode.Alpha6, KeyCode.Alpha7, KeyCode.Alpha8
        };
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugOutput = false;
        
        // Input Actions
        private InputAction inventoryToggleAction;
        private InputAction dropItemAction;
        private InputAction useItemAction;
        private InputAction scrollHotbarAction;
        private InputAction[] hotbarActions;
        
        // Components
        private PlayerInventory playerInventory;
        
        // State
        private bool inputEnabled = true;
        
        private void Awake()
        {
            SetupComponents();
            SetupInputActions();
        }
        
        private void SetupComponents()
        {
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogError("InventoryInputController: No PlayerInventory component found!");
            }
        }
        
        private void SetupInputActions()
        {
            // Inventory toggle
            inventoryToggleAction = new InputAction(binding: $"<Keyboard>/{inventoryToggleKey.ToString().ToLower()}");
            inventoryToggleAction.AddBinding("<Gamepad>/start"); // Menu button
            inventoryToggleAction.performed += OnInventoryToggle;
            
            // Drop item
            dropItemAction = new InputAction(binding: $"<Keyboard>/{dropItemKey.ToString().ToLower()}");
            dropItemAction.AddBinding("<Gamepad>/buttonEast"); // B button
            dropItemAction.performed += OnDropItem;
            
            // Use item
            useItemAction = new InputAction(binding: "<Mouse>/leftButton");
            useItemAction.AddBinding("<Gamepad>/rightTrigger");
            useItemAction.performed += OnUseItem;
            
            // Hotbar scrolling
            scrollHotbarAction = new InputAction(binding: "<Mouse>/scroll/y");
            scrollHotbarAction.AddBinding("<Gamepad>/dpad");
            scrollHotbarAction.performed += OnScrollHotbar;
            
            // Individual hotbar keys
            SetupHotbarActions();
            
            EnableInput();
        }
        
        private void SetupHotbarActions()
        {
            hotbarActions = new InputAction[hotbarKeys.Length];
            
            for (int i = 0; i < hotbarKeys.Length; i++)
            {
                int slotIndex = i; // Capture for closure
                hotbarActions[i] = new InputAction(binding: $"<Keyboard>/{hotbarKeys[i].ToString().ToLower()}");
                
                // Add gamepad bindings for first 4 slots
                if (i < 4)
                {
                    switch (i)
                    {
                        case 0: hotbarActions[i].AddBinding("<Gamepad>/dpad/up"); break;
                        case 1: hotbarActions[i].AddBinding("<Gamepad>/dpad/right"); break;
                        case 2: hotbarActions[i].AddBinding("<Gamepad>/dpad/down"); break;
                        case 3: hotbarActions[i].AddBinding("<Gamepad>/dpad/left"); break;
                    }
                }
                
                hotbarActions[i].performed += (context) => OnHotbarKeyPressed(slotIndex);
            }
        }
        
        private void OnInventoryToggle(InputAction.CallbackContext context)
        {
            if (!inputEnabled || playerInventory == null) return;
            
            playerInventory.ToggleInventory();
            
            if (enableDebugOutput)
            {
                Debug.Log($"Inventory toggled: {playerInventory.IsInventoryOpen}");
            }
        }
        
        private void OnDropItem(InputAction.CallbackContext context)
        {
            if (!inputEnabled || playerInventory == null) return;
            
            // Drop the currently selected hotbar item
            int selectedSlot = playerInventory.SelectedHotbarSlot;
            bool success = playerInventory.DropItem(1, selectedSlot, 1); // Container 1 = hotbar
            
            if (enableDebugOutput && success)
            {
                Debug.Log($"Dropped item from hotbar slot {selectedSlot}");
            }
        }
        
        private void OnUseItem(InputAction.CallbackContext context)
        {
            if (!inputEnabled || playerInventory == null) return;
            
            // Don't use items if inventory is open (to avoid conflicts with UI)
            if (playerInventory.IsInventoryOpen) return;
            
            bool success = playerInventory.UseSelectedItem();
            
            if (enableDebugOutput && success)
            {
                Debug.Log($"Used item from hotbar slot {playerInventory.SelectedHotbarSlot}");
            }
        }
        
        private void OnScrollHotbar(InputAction.CallbackContext context)
        {
            if (!inputEnabled || playerInventory == null) return;

            // Don't scroll hotbar if inventory is open
            if (playerInventory.IsInventoryOpen) return;

            // Only process on performed phase to avoid duplicate events
            if (context.phase != InputActionPhase.Performed) return;

            // Check if mouse is over UI elements (excluding hotbar)
            if (IsMouseOverInventoryUI()) return;

            float scrollValue = context.ReadValue<float>();

            // Add a small threshold to avoid micro-movements
            if (Mathf.Abs(scrollValue) < 0.1f) return;

            int currentSlot = playerInventory.SelectedHotbarSlot;
            int newSlot = currentSlot;

            if (scrollValue < 0) // Scroll up
            {
                newSlot = (currentSlot + 1) % playerInventory.Hotbar.SlotCount;
            }
            else if (scrollValue > 0) // Scroll down
            {
                newSlot = (currentSlot - 1 + playerInventory.Hotbar.SlotCount) % playerInventory.Hotbar.SlotCount;
            }

            if (newSlot != currentSlot)
            {
                playerInventory.SelectHotbarSlot(newSlot);

                if (enableDebugOutput)
                {
                    Debug.Log($"Selected hotbar slot {newSlot} (scroll: {scrollValue})");
                }
            }
        }
        
        private void OnHotbarKeyPressed(int slotIndex)
        {
            if (!inputEnabled || playerInventory == null) return;
            
            // Don't change hotbar selection if inventory is open
            if (playerInventory.IsInventoryOpen) return;
            
            if (slotIndex >= 0 && slotIndex < playerInventory.Hotbar.SlotCount)
            {
                playerInventory.SelectHotbarSlot(slotIndex);
                
                if (enableDebugOutput)
                {
                    Debug.Log($"Selected hotbar slot {slotIndex} via key press");
                }
            }
        }
        
        /// <summary>
        /// Enables or disables input processing
        /// </summary>
        public void SetInputEnabled(bool enabled)
        {
            inputEnabled = enabled;

            if (enabled)
            {
                EnableInput();
            }
            else
            {
                DisableInput();
            }
        }

        /// <summary>
        /// Checks if the mouse is over inventory UI elements (excluding hotbar)
        /// </summary>
        private bool IsMouseOverInventoryUI()
        {
            // Check if mouse is over any UI element
            if (EventSystem.current == null) return false;

            var pointerData = new PointerEventData(EventSystem.current)
            {
                position = Mouse.current.position.ReadValue()
            };

            var results = new System.Collections.Generic.List<RaycastResult>();
            EventSystem.current.RaycastAll(pointerData, results);

            // Check if any of the UI elements are inventory-related (but not hotbar)
            foreach (var result in results)
            {
                if (result.gameObject != null)
                {
                    // Allow hotbar interactions, block other inventory UI
                    var slotUI = result.gameObject.GetComponent<InventorySlotUI>();
                    if (slotUI != null && slotUI.Container != null)
                    {
                        // Allow hotbar, block backpack and equipment
                        if (slotUI.Container.ContainerType == ContainerType.General ||
                            slotUI.Container.ContainerType == ContainerType.Equipment)
                        {
                            return true;
                        }
                    }

                    // Block if mouse is over inventory panel (but not hotbar panel)
                    if (result.gameObject.name.Contains("Inventory") &&
                        !result.gameObject.name.Contains("Hotbar"))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        
        private void EnableInput()
        {
            inventoryToggleAction?.Enable();
            dropItemAction?.Enable();
            useItemAction?.Enable();
            scrollHotbarAction?.Enable();
            
            if (hotbarActions != null)
            {
                foreach (var action in hotbarActions)
                {
                    action?.Enable();
                }
            }
        }
        
        private void DisableInput()
        {
            inventoryToggleAction?.Disable();
            dropItemAction?.Disable();
            useItemAction?.Disable();
            scrollHotbarAction?.Disable();
            
            if (hotbarActions != null)
            {
                foreach (var action in hotbarActions)
                {
                    action?.Disable();
                }
            }
        }
        
        private void OnEnable()
        {
            if (inputEnabled)
            {
                EnableInput();
            }
        }
        
        private void OnDisable()
        {
            DisableInput();
        }
        
        private void OnDestroy()
        {
            // Dispose of all input actions
            inventoryToggleAction?.Dispose();
            dropItemAction?.Dispose();
            useItemAction?.Dispose();
            scrollHotbarAction?.Dispose();
            
            if (hotbarActions != null)
            {
                foreach (var action in hotbarActions)
                {
                    action?.Dispose();
                }
            }
        }
        
        // Public properties for external access
        public bool InputEnabled => inputEnabled;
        public KeyCode InventoryToggleKey => inventoryToggleKey;
        public KeyCode DropItemKey => dropItemKey;
        public KeyCode UseItemKey => useItemKey;
        public KeyCode[] HotbarKeys => hotbarKeys;
    }
}
