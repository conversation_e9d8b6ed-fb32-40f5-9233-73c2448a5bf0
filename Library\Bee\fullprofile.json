{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 2663, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 2663, "ts": 1751312097034671, "dur": 995, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097039553, "dur": 759, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751312096185691, "dur": 6937, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751312096192635, "dur": 60626, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751312096253276, "dur": 58252, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097040317, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096183391, "dur": 5874, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096189269, "dur": 832732, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096190287, "dur": 3049, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096193348, "dur": 1878, "ph": "X", "name": "ProcessMessages 15725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195233, "dur": 368, "ph": "X", "name": "ReadAsync 15725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195605, "dur": 12, "ph": "X", "name": "ProcessMessages 20543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195622, "dur": 134, "ph": "X", "name": "ReadAsync 20543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195758, "dur": 1, "ph": "X", "name": "ProcessMessages 1919", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195760, "dur": 77, "ph": "X", "name": "ReadAsync 1919", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195839, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195841, "dur": 36, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195879, "dur": 33, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195916, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195955, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096195994, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196042, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196043, "dur": 34, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196080, "dur": 37, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196118, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196120, "dur": 36, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196158, "dur": 72, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196234, "dur": 75, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196311, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196374, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196376, "dur": 93, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196473, "dur": 2, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196476, "dur": 101, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196585, "dur": 5, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196591, "dur": 89, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196684, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196686, "dur": 89, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196780, "dur": 2, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196784, "dur": 48, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196834, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196836, "dur": 41, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196880, "dur": 24, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196907, "dur": 44, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096196953, "dur": 75, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197032, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197035, "dur": 71, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197108, "dur": 1, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197111, "dur": 33, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197148, "dur": 71, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197222, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197225, "dur": 47, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197273, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197275, "dur": 38, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197315, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197316, "dur": 30, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197349, "dur": 32, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197383, "dur": 38, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197424, "dur": 57, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197486, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197489, "dur": 66, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197556, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197558, "dur": 50, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197611, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197614, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197652, "dur": 87, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197743, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197746, "dur": 52, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197799, "dur": 1, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197801, "dur": 75, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197881, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197884, "dur": 49, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197934, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197936, "dur": 33, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096197972, "dur": 41, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198017, "dur": 56, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198076, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198080, "dur": 61, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198143, "dur": 1, "ph": "X", "name": "ProcessMessages 1263", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198145, "dur": 40, "ph": "X", "name": "ReadAsync 1263", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198186, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198188, "dur": 46, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198236, "dur": 37, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198275, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198277, "dur": 35, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198315, "dur": 30, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198348, "dur": 40, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198389, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198391, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198427, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198428, "dur": 68, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198508, "dur": 5, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198516, "dur": 91, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198611, "dur": 2, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198614, "dur": 77, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198696, "dur": 4, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198701, "dur": 36, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198739, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198741, "dur": 60, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198806, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198807, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198874, "dur": 2, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198877, "dur": 56, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198936, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198938, "dur": 39, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198980, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096198982, "dur": 50, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199035, "dur": 40, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199076, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199077, "dur": 29, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199109, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199141, "dur": 25, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199168, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199203, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199239, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199243, "dur": 63, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199311, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199373, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199375, "dur": 34, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199411, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199413, "dur": 29, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199445, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199477, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199514, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199516, "dur": 48, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199566, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199568, "dur": 29, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199601, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199634, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199636, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199669, "dur": 68, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199742, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199747, "dur": 64, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199812, "dur": 1, "ph": "X", "name": "ProcessMessages 1207", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199814, "dur": 166, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199985, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096199987, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200094, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200097, "dur": 79, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200178, "dur": 2, "ph": "X", "name": "ProcessMessages 1401", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200181, "dur": 37, "ph": "X", "name": "ReadAsync 1401", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200221, "dur": 34, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200257, "dur": 28, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200288, "dur": 37, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200330, "dur": 35, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200368, "dur": 53, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200426, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200430, "dur": 50, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200482, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200485, "dur": 32, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200520, "dur": 41, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200564, "dur": 34, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200601, "dur": 34, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200640, "dur": 31, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200673, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200674, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200710, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200742, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200778, "dur": 31, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200810, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200813, "dur": 33, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200847, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200849, "dur": 26, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200878, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200911, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200913, "dur": 31, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200947, "dur": 29, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096200978, "dur": 22, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201004, "dur": 28, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201034, "dur": 30, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201067, "dur": 31, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201101, "dur": 29, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201133, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201163, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201195, "dur": 32, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201231, "dur": 30, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201263, "dur": 30, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201296, "dur": 28, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201327, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201371, "dur": 1, "ph": "X", "name": "ProcessMessages 1009", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201372, "dur": 32, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201408, "dur": 31, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201440, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201442, "dur": 28, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201474, "dur": 65, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201546, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201550, "dur": 64, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201616, "dur": 1, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201618, "dur": 32, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201653, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201687, "dur": 42, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201731, "dur": 35, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201770, "dur": 34, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201806, "dur": 27, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201836, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201873, "dur": 52, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201929, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201931, "dur": 38, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201972, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096201973, "dur": 31, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202007, "dur": 33, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202043, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202084, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202085, "dur": 33, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202121, "dur": 48, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202170, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202172, "dur": 30, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202205, "dur": 30, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202237, "dur": 32, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202272, "dur": 42, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202316, "dur": 43, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202362, "dur": 29, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202394, "dur": 32, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202428, "dur": 51, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202483, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202487, "dur": 74, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202564, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202566, "dur": 43, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202612, "dur": 34, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202649, "dur": 34, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202685, "dur": 31, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202719, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202754, "dur": 36, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202793, "dur": 53, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202847, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202850, "dur": 48, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202900, "dur": 31, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202934, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096202967, "dur": 35, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203004, "dur": 54, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203059, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203061, "dur": 48, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203111, "dur": 38, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203152, "dur": 77, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203231, "dur": 40, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203273, "dur": 34, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203310, "dur": 29, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203341, "dur": 35, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203379, "dur": 32, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203414, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203447, "dur": 56, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203505, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203507, "dur": 33, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203543, "dur": 43, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203588, "dur": 36, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203626, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203627, "dur": 35, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203664, "dur": 33, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203700, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203731, "dur": 35, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203768, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203801, "dur": 32, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203836, "dur": 35, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203873, "dur": 33, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203908, "dur": 31, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203942, "dur": 44, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096203989, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204036, "dur": 26, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204064, "dur": 56, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204124, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204126, "dur": 41, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204170, "dur": 31, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204204, "dur": 33, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204240, "dur": 30, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204273, "dur": 93, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204368, "dur": 33, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204403, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204436, "dur": 31, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204469, "dur": 58, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204529, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204531, "dur": 45, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204578, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204579, "dur": 29, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204637, "dur": 46, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204684, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204688, "dur": 37, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204726, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204727, "dur": 30, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204760, "dur": 33, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204796, "dur": 32, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204830, "dur": 41, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204872, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204874, "dur": 37, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204914, "dur": 33, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204949, "dur": 30, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096204981, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205014, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205051, "dur": 32, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205085, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205088, "dur": 37, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205128, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205160, "dur": 32, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205197, "dur": 81, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205280, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205282, "dur": 35, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205320, "dur": 30, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205353, "dur": 48, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205403, "dur": 52, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205457, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205496, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205497, "dur": 53, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205553, "dur": 58, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205615, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205618, "dur": 66, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205688, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205691, "dur": 44, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205737, "dur": 1, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205739, "dur": 58, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205801, "dur": 35, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205839, "dur": 29, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205871, "dur": 40, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205913, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096205962, "dur": 40, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206004, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206005, "dur": 39, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206047, "dur": 30, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206079, "dur": 30, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206112, "dur": 46, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206161, "dur": 38, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206201, "dur": 32, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206236, "dur": 33, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206272, "dur": 31, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206305, "dur": 47, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206354, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206356, "dur": 35, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206393, "dur": 33, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206429, "dur": 29, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206460, "dur": 63, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206527, "dur": 47, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206576, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206577, "dur": 53, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206632, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206634, "dur": 33, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206672, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206718, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206719, "dur": 29, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206751, "dur": 36, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206790, "dur": 31, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206823, "dur": 35, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206858, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206860, "dur": 44, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206913, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206918, "dur": 66, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206987, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096206991, "dur": 45, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207038, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207040, "dur": 45, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207087, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207090, "dur": 43, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207135, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207137, "dur": 44, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207184, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207186, "dur": 74, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207263, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207265, "dur": 36, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207304, "dur": 35, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207341, "dur": 34, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207378, "dur": 35, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207416, "dur": 37, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207454, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207456, "dur": 39, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207497, "dur": 34, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207534, "dur": 42, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207578, "dur": 102, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207683, "dur": 39, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207723, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207726, "dur": 42, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207771, "dur": 33, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207807, "dur": 39, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207849, "dur": 39, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207889, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207891, "dur": 45, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096207939, "dur": 72, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208013, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208014, "dur": 52, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208068, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208070, "dur": 57, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208130, "dur": 53, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208193, "dur": 3, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208197, "dur": 58, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208256, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208258, "dur": 31, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208291, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208292, "dur": 29, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208324, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208356, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208410, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208462, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208464, "dur": 33, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208500, "dur": 108, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208615, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208685, "dur": 2, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208689, "dur": 62, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208756, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208758, "dur": 42, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208804, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208842, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208844, "dur": 29, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208876, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208907, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096208989, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209045, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209047, "dur": 31, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209081, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209152, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209189, "dur": 33, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209225, "dur": 30, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209258, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209332, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209374, "dur": 38, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209415, "dur": 29, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209446, "dur": 59, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209508, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209557, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209559, "dur": 42, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209603, "dur": 35, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209643, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209721, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209724, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209764, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209802, "dur": 72, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209876, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209916, "dur": 33, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209952, "dur": 30, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096209984, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210056, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210094, "dur": 34, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210131, "dur": 28, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210162, "dur": 64, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210228, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210262, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210293, "dur": 37, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210333, "dur": 30, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210366, "dur": 67, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210435, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210472, "dur": 33, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210509, "dur": 29, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210540, "dur": 80, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210623, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210658, "dur": 46, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210706, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210709, "dur": 31, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210742, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210809, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210845, "dur": 34, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210882, "dur": 70, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210954, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096210989, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211019, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211058, "dur": 56, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211116, "dur": 41, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211160, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211196, "dur": 32, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211232, "dur": 31, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211265, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211335, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211384, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211424, "dur": 27, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211454, "dur": 65, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211522, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211561, "dur": 38, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211602, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211604, "dur": 72, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211679, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211719, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211721, "dur": 39, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211763, "dur": 29, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211794, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211796, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211856, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211929, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211931, "dur": 39, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096211973, "dur": 29, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212006, "dur": 70, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212078, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212133, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212135, "dur": 93, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212235, "dur": 3, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212239, "dur": 38, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212279, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212281, "dur": 30, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212314, "dur": 31, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212348, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212379, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212453, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212487, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212488, "dur": 42, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212532, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212533, "dur": 82, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212618, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212671, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212672, "dur": 41, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212715, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212717, "dur": 41, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212761, "dur": 34, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212796, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212798, "dur": 34, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096212834, "dur": 311, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213149, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213251, "dur": 2, "ph": "X", "name": "ProcessMessages 3482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213254, "dur": 39, "ph": "X", "name": "ReadAsync 3482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213295, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213296, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213330, "dur": 123, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213457, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213525, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213527, "dur": 62, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213591, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213629, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213630, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213673, "dur": 105, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213780, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213829, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213831, "dur": 39, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213873, "dur": 31, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213906, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213908, "dur": 60, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096213970, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214003, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214005, "dur": 87, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214105, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214110, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214189, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214254, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214257, "dur": 48, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214308, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214309, "dur": 71, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214383, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214432, "dur": 35, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214469, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214470, "dur": 73, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214546, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214594, "dur": 41, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214637, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214638, "dur": 27, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214669, "dur": 66, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214737, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214768, "dur": 39, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214810, "dur": 34, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214847, "dur": 81, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214931, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096214969, "dur": 84, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215056, "dur": 46, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215103, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215105, "dur": 36, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215145, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215197, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215198, "dur": 56, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215257, "dur": 78, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215341, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215397, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215400, "dur": 35, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215438, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215490, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215530, "dur": 53, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215586, "dur": 80, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215668, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215706, "dur": 34, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215743, "dur": 28, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215773, "dur": 63, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215839, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215877, "dur": 56, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215936, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096215938, "dur": 64, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216005, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216041, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216043, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216077, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216109, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216182, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216219, "dur": 45, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216267, "dur": 81, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216351, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216386, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216388, "dur": 46, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216438, "dur": 76, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216518, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216557, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216559, "dur": 116, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216676, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216677, "dur": 63, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216745, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216781, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216782, "dur": 35, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216820, "dur": 74, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216898, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216934, "dur": 31, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216968, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096216998, "dur": 66, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217067, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217103, "dur": 38, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217143, "dur": 76, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217223, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217259, "dur": 51, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217313, "dur": 73, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217389, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217428, "dur": 32, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217465, "dur": 25, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217491, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217493, "dur": 61, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217558, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217593, "dur": 54, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217655, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217659, "dur": 32, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217694, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217696, "dur": 42, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217743, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217782, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217785, "dur": 29, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217816, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217818, "dur": 71, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217893, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217928, "dur": 36, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217966, "dur": 26, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096217996, "dur": 68, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218066, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218068, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218109, "dur": 4, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218114, "dur": 81, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218199, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218201, "dur": 107, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218317, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218373, "dur": 2, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218376, "dur": 52, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218430, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218432, "dur": 66, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218501, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218563, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218566, "dur": 44, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218613, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218670, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218713, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218715, "dur": 30, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218748, "dur": 117, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218869, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218905, "dur": 51, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096218959, "dur": 47, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219010, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219072, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219153, "dur": 4, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219159, "dur": 60, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219221, "dur": 3, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219226, "dur": 32, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219261, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219263, "dur": 55, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219322, "dur": 46, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219371, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219430, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219485, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219486, "dur": 45, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219534, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219594, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219655, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219657, "dur": 58, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219717, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219719, "dur": 51, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219772, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219774, "dur": 48, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219827, "dur": 61, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219892, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219936, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219939, "dur": 29, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096219971, "dur": 55, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220027, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220028, "dur": 64, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220095, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220096, "dur": 46, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220145, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220218, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220259, "dur": 136, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220401, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220463, "dur": 397, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220864, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220929, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096220930, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221192, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221286, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221293, "dur": 43, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221338, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221342, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221386, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221388, "dur": 46, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221445, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221448, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221489, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221491, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221553, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221556, "dur": 54, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221613, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221617, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221662, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221667, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221718, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221720, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221775, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221778, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221825, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221828, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221870, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221874, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221907, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221909, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221972, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096221974, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222029, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222032, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222083, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222086, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222136, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222140, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222200, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222204, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222312, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222317, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222380, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222383, "dur": 54, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222441, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222445, "dur": 46, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222495, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222498, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222541, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222543, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222592, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222594, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222650, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222654, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222709, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222713, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222766, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222770, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222826, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222829, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222879, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222884, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222913, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222915, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222944, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222984, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096222987, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223047, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223051, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223112, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223117, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223162, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223165, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223214, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223217, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223270, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223272, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223330, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223333, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223380, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223384, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223434, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223437, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223483, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223487, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223537, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223586, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223588, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223645, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223649, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223703, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223706, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223736, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223780, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223786, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223834, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223838, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223888, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223895, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223951, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096223953, "dur": 45, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224002, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224006, "dur": 51, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224060, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224064, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224108, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224112, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224161, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224163, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224217, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224221, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224269, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224271, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224315, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224318, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224369, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224372, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224427, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224430, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224483, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224487, "dur": 44, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224536, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224539, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224594, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224598, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224640, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224642, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224695, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224697, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224752, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224754, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224803, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224805, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224854, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224858, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224900, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224903, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224953, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096224956, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225006, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225009, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225063, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225066, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225095, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225097, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225126, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225189, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225192, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225275, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225278, "dur": 65, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225347, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225350, "dur": 61, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225414, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225416, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225468, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225529, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225536, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225583, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225586, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225628, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225630, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225687, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225691, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225724, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225728, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225766, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225769, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225817, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225820, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225872, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225875, "dur": 61, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225942, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096225945, "dur": 94, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226044, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226048, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226101, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226103, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226164, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226167, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226234, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226236, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226285, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226288, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226343, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226347, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226383, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226386, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226427, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226429, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226489, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226492, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226537, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226539, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226590, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226594, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226650, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226653, "dur": 89, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226746, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226750, "dur": 86, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226840, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096226846, "dur": 154, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096227009, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096227012, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096227062, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096227066, "dur": 17090, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244166, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244171, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244260, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244264, "dur": 588, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244862, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244867, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244968, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096244972, "dur": 217, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096245196, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096245199, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096245236, "dur": 1141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246397, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246403, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246494, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246497, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246540, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246697, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096246724, "dur": 445, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247178, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247181, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247218, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247275, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247278, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247586, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247589, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247636, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247639, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247676, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247678, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247857, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247900, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247902, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247938, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096247974, "dur": 332, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248316, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248320, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248357, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248361, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248424, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248473, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248476, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248532, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248564, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248604, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248607, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248719, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248765, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248810, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248873, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248927, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248929, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248971, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096248974, "dur": 110, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249088, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249091, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249137, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249242, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249282, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249285, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249319, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249322, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249366, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249405, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249408, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249441, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249444, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249597, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249601, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249652, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249654, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249687, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249689, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249821, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249856, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249890, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096249893, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250103, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250140, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250143, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250179, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250181, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250224, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250226, "dur": 231, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250460, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250462, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250511, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250515, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250551, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250643, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250675, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250708, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250711, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250822, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250859, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250861, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250915, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250966, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096250968, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251001, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251002, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251037, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251039, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251087, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251133, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251135, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251175, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251177, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251223, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251267, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251349, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251385, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251424, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251459, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251523, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251562, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251564, "dur": 266, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251834, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251836, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251884, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251920, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096251970, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252002, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252164, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252166, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252210, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252212, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252255, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252258, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252309, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252312, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252349, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252351, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252389, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252424, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252458, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252460, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252487, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252523, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252525, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252564, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252624, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252670, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252725, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252759, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252948, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252994, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096252996, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253054, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253056, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253109, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253155, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253214, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253248, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253280, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253510, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253545, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253547, "dur": 245, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253800, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253851, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096253883, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254157, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254160, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254198, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254337, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096254390, "dur": 817, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255220, "dur": 125, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255353, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255358, "dur": 118, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255481, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255548, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255550, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255845, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255848, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255896, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255933, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096255936, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256063, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256128, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256131, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256185, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256188, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256336, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256403, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256405, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256454, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256456, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256673, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256720, "dur": 254, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256985, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096256990, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257047, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257049, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257106, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257275, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257314, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257315, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257366, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257451, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257497, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257540, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257542, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257775, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096257798, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258178, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258237, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258240, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258347, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258392, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258395, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258445, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258480, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258609, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096258644, "dur": 706, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096259355, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096259383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096259384, "dur": 187, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096259574, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096259604, "dur": 713, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260327, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260389, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260450, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260502, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260506, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260569, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260571, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260756, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260832, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096260835, "dur": 848, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096261687, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096261689, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096261749, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096261755, "dur": 65081, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096326850, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096326856, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096326915, "dur": 2780, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096329705, "dur": 11586, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341305, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341309, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341362, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341364, "dur": 528, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341904, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096341940, "dur": 1896, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343846, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343852, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343896, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343898, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343946, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096343950, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344015, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344017, "dur": 617, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344643, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344648, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344682, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096344684, "dur": 1856, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346557, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346562, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346620, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346622, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346675, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346678, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346707, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346955, "dur": 37, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096346995, "dur": 457, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096347457, "dur": 579, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348045, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348049, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348091, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348093, "dur": 478, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348581, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096348613, "dur": 670, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349295, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349331, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349333, "dur": 157, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349496, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349542, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349654, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096349688, "dur": 552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350248, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350252, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350294, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350459, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350463, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350497, "dur": 386, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350892, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096350929, "dur": 820, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351757, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351761, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351800, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351804, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351930, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351934, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351977, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096351979, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096352018, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096352020, "dur": 611, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096352641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096352646, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096352685, "dur": 627, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353322, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353326, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353395, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353397, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353442, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096353444, "dur": 666, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354118, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354122, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354180, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354183, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354249, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354251, "dur": 479, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354742, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354748, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354793, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096354941, "dur": 547, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355500, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355505, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355556, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355557, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355607, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355611, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355645, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355719, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355746, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355748, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355871, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355922, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355925, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355981, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096355983, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356032, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356034, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356084, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356161, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356164, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356200, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356251, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356310, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356312, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356370, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356372, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356417, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356419, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356460, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356678, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356756, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356759, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356806, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356837, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356918, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356977, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096356979, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357022, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357027, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357073, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357075, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357112, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357114, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357154, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357157, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357196, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357198, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357251, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357254, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357287, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357289, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357336, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357339, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357385, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357388, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357437, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357440, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357485, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357488, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357544, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357546, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357617, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357621, "dur": 59, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357684, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357687, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357744, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357748, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357820, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357823, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357878, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357880, "dur": 47, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096357931, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096358049, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096358053, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096358114, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096358117, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096358155, "dur": 163650, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096521814, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096521819, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096521859, "dur": 22, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096521882, "dur": 26649, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096548542, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096548547, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096548584, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096548588, "dur": 178385, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096726987, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096726993, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096727098, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096727105, "dur": 15031, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096742151, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096742155, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096742194, "dur": 19, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096742214, "dur": 39761, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096781985, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096781988, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096782030, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096782034, "dur": 44442, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096826488, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096826494, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096826595, "dur": 24, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096826620, "dur": 29325, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096855960, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096855969, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096855998, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096856002, "dur": 2533, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096858548, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096858554, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096858650, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096858683, "dur": 10449, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096869143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096869147, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096869182, "dur": 19, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096869202, "dur": 26715, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096895925, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096895929, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096896044, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096896048, "dur": 991, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096897054, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096897060, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096897154, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096897184, "dur": 11695, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096908890, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096908894, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096908969, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096908975, "dur": 1079, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096910064, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096910069, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096910112, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312096910142, "dur": 96418, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097006569, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097006574, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097006669, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097006675, "dur": 1047, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097007738, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097007743, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097007835, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097007872, "dur": 621, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097008501, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097008506, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097008599, "dur": 843, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751312097009451, "dur": 11849, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097040329, "dur": 3303, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 8589934592, "ts": 1751312096180500, "dur": 131119, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751312096311622, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751312096311629, "dur": 1563, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097043637, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 4294967296, "ts": 1751312096152600, "dur": 870453, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751312096156987, "dur": 10478, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751312097023158, "dur": 6810, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751312097027186, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751312097030072, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097043648, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751312096185901, "dur": 64, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312096185995, "dur": 2466, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312096188480, "dur": 914, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312096189533, "dur": 90, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751312096189623, "dur": 534, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312096191179, "dur": 258, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096192410, "dur": 2208, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096194694, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096195576, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751312096195984, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751312096196116, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751312096199007, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096199177, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096199602, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751312096202099, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096202214, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096203336, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751312096206282, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751312096206643, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751312096211202, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751312096212221, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751312096215626, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751312096190191, "dur": 29075, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312096219283, "dur": 787520, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097006805, "dur": 186, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097006991, "dur": 129, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097007126, "dur": 80, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097007364, "dur": 67, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097007472, "dur": 2056, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751312096190951, "dur": 28424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096219388, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096220449, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096220528, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096220651, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096220710, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096220850, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096221040, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096221191, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096221424, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096221529, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096221694, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096221759, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096222064, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096222395, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096222617, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096222699, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096222844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096222901, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096223026, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096223149, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096223346, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096223454, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096223514, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751312096223700, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096223883, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096223986, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751312096224096, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096224213, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096224437, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751312096224545, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096224611, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096224662, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096225253, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751312096225432, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096225698, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096225799, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096226037, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096228466, "dur": 2598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096231065, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096231930, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096232243, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096232482, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096233485, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096233854, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096234549, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096234856, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096235202, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096235649, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096235985, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096236247, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096236525, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096236947, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096237209, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096237524, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096237769, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096238068, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096238450, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096238872, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096239147, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096239508, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096239864, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096240196, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096241435, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096242273, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096242512, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096242772, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096244719, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096245445, "dur": 1191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096246639, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096247369, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096248261, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096248339, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096248439, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096249077, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096249146, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096249964, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096250114, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096250187, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096250413, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096250574, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096251183, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096251249, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096251309, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096251924, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096252022, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096252644, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751312096252817, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096252910, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096253725, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096253825, "dur": 83810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096337638, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096340239, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096340294, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096342824, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096342905, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096345469, "dur": 6837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096352307, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096352371, "dur": 3689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751312096356184, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096356362, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096356437, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751312096356815, "dur": 497654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751312096854503, "dur": 405, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751312096854471, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751312096854957, "dur": 2573, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751312096857538, "dur": 149306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096191103, "dur": 28318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096219438, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096220304, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096220470, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096220535, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096220659, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221019, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221130, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221183, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096221351, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221449, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221623, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096221681, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096221852, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096221947, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096222025, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096222100, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096222480, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096222715, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096222885, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096223098, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096223181, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096223343, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096223652, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096223847, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096224143, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096224211, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751312096224391, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096224618, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096224705, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096224868, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096225368, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751312096225431, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096225655, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096225789, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096226357, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096228578, "dur": 2761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096231340, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096231660, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096232148, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096232500, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096233273, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096233781, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096234528, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096234782, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096235079, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096235333, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096235574, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096235849, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096236246, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096236477, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096236735, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096237116, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096237362, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096237667, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096237939, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096238195, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096238889, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096239176, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096239492, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096240162, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096241066, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096241951, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096242231, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096242501, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096242768, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096244555, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096245431, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096246353, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096246526, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096246616, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096247275, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096247526, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096247756, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096247833, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096248067, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096248126, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096248399, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096248717, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096249496, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096249669, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096250013, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096250213, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096250276, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096251140, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096251210, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096251315, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096251462, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096251528, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096252019, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096252319, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096252460, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096252539, "dur": 1547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096254087, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096254295, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096254505, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096255465, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096255652, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096255715, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096256434, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096256555, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751312096256794, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096257415, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096257485, "dur": 85389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096342878, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096345213, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096345298, "dur": 3586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096348886, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096349505, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096352282, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751312096352471, "dur": 4597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751312096357155, "dur": 649663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096190942, "dur": 28407, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096219363, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096220369, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096220504, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096220595, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096220667, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096220748, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096220890, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096220948, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096221005, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096221109, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096221191, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096221369, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096221441, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096221525, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096221604, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096221774, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096221878, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096222052, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096222382, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096222557, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096222728, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096223008, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096223248, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096223635, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096223699, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096223908, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096224212, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096224305, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096224365, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096224423, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096224613, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096224874, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096225002, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751312096225173, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096225248, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096225332, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6154019870087291391.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096225645, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751312096225797, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096226025, "dur": 2490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096228516, "dur": 2562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096231079, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096232103, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096232586, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096233021, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096233401, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096233689, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096233930, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096234609, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096234874, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096235145, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096235372, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096235603, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096235953, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096236220, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096236469, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096236782, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096237016, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096237283, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096237592, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096237835, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096238064, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096238372, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096238629, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096238858, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096239194, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096239554, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096239934, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096240761, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096241932, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096242299, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096242557, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096242793, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096243197, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096243690, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096243891, "dur": 1544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096245436, "dur": 1674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096247115, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096247307, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096247380, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096249120, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096249217, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096249899, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096249980, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096250875, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096251015, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096251297, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096252024, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096252156, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096252644, "dur": 1290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096253937, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096254092, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096254171, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096255001, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096255140, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751312096255364, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096255937, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096256060, "dur": 81632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096337694, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096340264, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096340349, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096343271, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096343669, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096346572, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096347054, "dur": 2687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096349743, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096349932, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096352676, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096353136, "dur": 3874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751312096357011, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751312096357096, "dur": 649741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096191402, "dur": 28212, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096219615, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096220332, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096220450, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096220503, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096220761, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096220856, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096220925, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096221073, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096221148, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096221230, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096221521, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096221592, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096221811, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096222036, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096222173, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096222351, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096222431, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096222484, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096222719, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096222989, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096223066, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096223203, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096223264, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096223347, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751312096223501, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096223653, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096223723, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096223872, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224047, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224137, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224192, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751312096224352, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224428, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224523, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224651, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224795, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751312096224860, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096224932, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096225037, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751312096225261, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751312096225713, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096225794, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096226415, "dur": 2680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096229096, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096229528, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096229801, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096230083, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096230337, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096231160, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096232038, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096232301, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096232637, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096233015, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096233410, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096233844, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\PropertyDrawers\\TransformNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751312096233766, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096234690, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096235020, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096235264, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096235535, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096235940, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096236186, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096236454, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096236733, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096237134, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096237388, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096237664, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096237908, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096238136, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096238503, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096238794, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096239373, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096239721, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096239969, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096240221, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096241162, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096242197, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096242480, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096242781, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096244754, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096245464, "dur": 895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096246370, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096246515, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096246634, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096247837, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096248000, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096248329, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096249777, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096250084, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096250145, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096250493, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096250561, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096251295, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096251412, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096251515, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096252027, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096252652, "dur": 5794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096258448, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751312096258641, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096259152, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096259308, "dur": 78852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096338163, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096340854, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096340936, "dur": 6524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096347462, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096347657, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096350685, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096350761, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096350821, "dur": 5259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751312096356082, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096356249, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096356387, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751312096356648, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751312096356914, "dur": 649872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096190833, "dur": 28465, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096219331, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096219437, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220069, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096220135, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220247, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220372, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096220430, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220489, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096220566, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220693, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096220780, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220883, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096220962, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096221022, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096221198, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096221486, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096221594, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096221649, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096221738, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096221948, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096222083, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096222139, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096222303, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096222393, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096222507, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096223103, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096223163, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751312096223280, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096223469, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096223533, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096223631, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096223807, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096223935, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096224015, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096224074, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096224683, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096224739, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096224851, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096224989, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751312096225136, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096225287, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096225362, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096225519, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751312096225806, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096226945, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096229641, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096229901, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096230120, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096230450, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096230748, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096231091, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096231887, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096232206, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096232503, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096233410, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096233727, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096234016, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096234698, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096235010, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096235257, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096235511, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096235807, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096236123, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096236489, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096236809, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096237057, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096237308, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096237625, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096237874, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096238121, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096238445, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096238730, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096239625, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096239928, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096240172, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096241229, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096242286, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096242537, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096242776, "dur": 2070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096244846, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096245433, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096246345, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096246565, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096246704, "dur": 1880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096248586, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096248664, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096249309, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096249596, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096249876, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096250182, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096252183, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096252313, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096252475, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096252533, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096254033, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751312096254203, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096254801, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096254891, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096254965, "dur": 88022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096342988, "dur": 2807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096345801, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096345972, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096348429, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096348541, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096350976, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096351040, "dur": 3378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096354419, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096354761, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096355062, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096355341, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096355477, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096355730, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096355886, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751312096356072, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751312096356188, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096356256, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096356382, "dur": 50, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751312096356433, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096356553, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096356644, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751312096356814, "dur": 170641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096527485, "dur": 193802, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751312096527458, "dur": 196166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096725540, "dur": 323, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751312096726890, "dur": 98590, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751312096854459, "dur": 151043, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751312096854448, "dur": 151056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751312097005529, "dur": 1206, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751312096190908, "dur": 28414, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096219369, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096219555, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096220166, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096220233, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096220304, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096220407, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096220484, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096220576, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096220700, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096220812, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221017, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221126, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221414, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221690, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221789, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096221850, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096221998, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096222109, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096222201, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096222467, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096222691, "dur": 20365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096243145, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096243227, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096243596, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096243929, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096245444, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096246614, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096246901, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096247354, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096248288, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096248413, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096248648, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096248870, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096249453, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096249568, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096249734, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096251152, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096251702, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096251780, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096252071, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096252631, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751312096252813, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096252880, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096253438, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096253586, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096253704, "dur": 62937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096316643, "dur": 20996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096337641, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096340179, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096340310, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096342940, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096343021, "dur": 2577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096345636, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096349154, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096349280, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096352175, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096352348, "dur": 3882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751312096356233, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096356489, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096356818, "dur": 537854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751312096894710, "dur": 13100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751312096894674, "dur": 13138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751312096907840, "dur": 1226, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751312096909072, "dur": 97723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096191126, "dur": 28335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096219478, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096220220, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096220324, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096220548, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096220688, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096220751, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096220838, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096220933, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096221027, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096221134, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096221193, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096221331, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096221419, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096221557, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096221618, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096221701, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096221916, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096222093, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096222360, "dur": 21427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096243874, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096243970, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096244139, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096244215, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096245439, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096245539, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096245731, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096246139, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096246355, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096246532, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096246592, "dur": 330, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096246936, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096247903, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096248478, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096248690, "dur": 818, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096249510, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096250649, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096250935, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096251146, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096251281, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096252477, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096252637, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096252827, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096253162, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096253809, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096253922, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096254092, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096254158, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096255084, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096255216, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096255366, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096255442, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096255979, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096256087, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751312096256309, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096257075, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096257202, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096257260, "dur": 80369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096337632, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096340179, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096340326, "dur": 2467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096342854, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096345596, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096345747, "dur": 6835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096352584, "dur": 676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096353279, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096356581, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751312096356712, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751312096356772, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751312096356865, "dur": 649940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096191041, "dur": 28349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096219402, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220150, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220230, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096220321, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220504, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096220570, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220657, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096220718, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220807, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096220932, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096221400, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096221508, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096221588, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096221721, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096221844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222064, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096222260, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751312096222326, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222515, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222616, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222694, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222765, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222894, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096222951, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096223057, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096223264, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096223369, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751312096223789, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096224031, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096224106, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096224192, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096224440, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096224584, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096224991, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096225247, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096225429, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096225553, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096225650, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751312096225705, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096226088, "dur": 2693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096228782, "dur": 2481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096231264, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096232233, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096232481, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096233390, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096233683, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096233925, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096234629, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096235018, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096235262, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096235510, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096235782, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096236077, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096236401, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096236663, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096236945, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096237237, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096237673, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\Utilities\\TextureGradient.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751312096237546, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096238441, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096238774, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096239387, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096239751, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096240005, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096240626, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096241889, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096242330, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096242579, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096243818, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096243913, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096245486, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096246366, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096246517, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096246617, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096247279, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096247417, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096247481, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096247757, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096248226, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096248287, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096249071, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096249526, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096249599, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096249714, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096250063, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096250178, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096250928, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096251323, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096251434, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751312096251536, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096252045, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096252649, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751312096252805, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096252901, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096253458, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096253614, "dur": 58587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096314904, "dur": 375, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1751312096315280, "dur": 1216, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1751312096316497, "dur": 100, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1751312096312203, "dur": 4407, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096316613, "dur": 21031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096337649, "dur": 2552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096340203, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096340334, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096342772, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096342902, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096345461, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096345654, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096345724, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096348205, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096348313, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096350783, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096350963, "dur": 3849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751312096354813, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096355036, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751312096355124, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751312096355313, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751312096355862, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751312096355941, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751312096356367, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751312096356740, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751312096356835, "dur": 649956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096191196, "dur": 28308, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096219519, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220169, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220271, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096220380, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220474, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220549, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220764, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096220880, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221054, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221158, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221211, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096221379, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221439, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096221500, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221599, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221658, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096221741, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096221900, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096222052, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751312096222211, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096222366, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096222476, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096222717, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096222976, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751312096223146, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096223285, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751312096223574, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096223688, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096223798, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096223923, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096224019, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096224138, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096224357, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096224564, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096224746, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096224971, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096225204, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096225265, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096225520, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096225639, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096225733, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751312096225785, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096226057, "dur": 2042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096228101, "dur": 3076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096231178, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096232140, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096232395, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096233162, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096233585, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096233840, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096234534, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096234808, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096235242, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096235500, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096235778, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096236180, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096236486, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096236772, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096237160, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096237520, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096237747, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096237994, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096238245, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096238580, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096238793, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096239038, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096239518, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096239961, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096240647, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096241582, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096242472, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096243149, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096243579, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096243900, "dur": 1528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096245429, "dur": 1141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096246582, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096246854, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096246960, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096247738, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096247942, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096248154, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096248643, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096248849, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096248936, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096249798, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096249879, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096250019, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096250333, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096250400, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096251484, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096251558, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096251650, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.Editor.ref.dll_FBEF939465B2E7A7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096251727, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096252049, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096252651, "dur": 2822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096255475, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751312096255633, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096255707, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096256226, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096256369, "dur": 81302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096337680, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096340195, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096340337, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096342772, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096342974, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096345362, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096345465, "dur": 2733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096348200, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096348321, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096350782, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096350959, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751312096354261, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096354777, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096354921, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096355015, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096355136, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751312096355205, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096355380, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751312096355471, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096355765, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751312096355899, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751312096356239, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096356425, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751312096356714, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751312096356799, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751312096357136, "dur": 649674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096191241, "dur": 28287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096219537, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220160, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096220260, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220469, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220559, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220626, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220728, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096220852, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096220946, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221049, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221131, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096221226, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221496, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221647, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221777, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221857, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096221915, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096222137, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751312096222268, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751312096222474, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751312096222617, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096222925, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223080, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223287, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751312096223394, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223549, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223661, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223749, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096223915, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751312096223973, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096224089, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751312096224143, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096224280, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751312096224508, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096224607, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751312096224668, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096224756, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096224867, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751312096224978, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096225106, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096225277, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096225483, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096225697, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096225810, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096228274, "dur": 2073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096230348, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096231001, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096231620, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096231983, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096232359, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096232613, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096232931, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096233362, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096233660, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096233920, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096234601, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096234902, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096235194, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096235687, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096236014, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096236279, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096236535, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096236837, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096237086, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096237329, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096237654, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096237903, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096238138, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096238454, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096238711, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096239746, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096240004, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096240247, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096241288, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096242280, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096242549, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096242786, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096243686, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096243924, "dur": 1515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096245440, "dur": 896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096246341, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096246566, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096246665, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096247334, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096247521, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096247598, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096247925, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096248369, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096248996, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096249351, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096249621, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096249807, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096249869, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096250896, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096251356, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096251545, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096251600, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096252073, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096252129, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096252646, "dur": 2510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096255167, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096255327, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096255399, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096257303, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096257414, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096257490, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096257665, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096258331, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096258428, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096258633, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096259383, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096259554, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751312096259777, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096260636, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096261897, "dur": 258927, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096527857, "dur": 19111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751312096527447, "dur": 19605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096547424, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096548638, "dur": 192496, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096749236, "dur": 28129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751312096749219, "dur": 29865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096780566, "dur": 372, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096781789, "dur": 86331, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751312096894656, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751312096894646, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751312096895992, "dur": 63, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751312096894893, "dur": 1225, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751312096896127, "dur": 110700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096191170, "dur": 28311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096219496, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220147, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220277, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220435, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220543, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220642, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096220732, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220845, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096220943, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096221219, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096221536, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096221627, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096221755, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096221847, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096221962, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096222025, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222110, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222293, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222406, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222506, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222658, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096222978, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751312096223234, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096223315, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751312096223576, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096223661, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751312096223769, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096223963, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096224044, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751312096224285, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751312096224554, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096224676, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751312096224737, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096224819, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096224891, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096225017, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751312096225229, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096225315, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096225402, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751312096225746, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096226050, "dur": 2538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096228589, "dur": 3050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096231640, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096232173, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096232508, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096233527, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096234059, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096234790, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096235433, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096235679, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096236119, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096236492, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096236852, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096237101, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096237383, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096237701, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096237964, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096238210, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096238485, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096238724, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096239600, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096239941, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096240797, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096241703, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096242149, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096242545, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096242784, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096244599, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096245452, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096246383, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096246581, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096246673, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096247296, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096247631, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096247908, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096248002, "dur": 1286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096249290, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096249479, "dur": 483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096250118, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096250476, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096251133, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096251201, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096251489, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096251956, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096252062, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096252634, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096252766, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096252832, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096253328, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096253480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096253551, "dur": 6011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096259565, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751312096259797, "dur": 77884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096337682, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096340179, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096340279, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096342850, "dur": 5764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096348615, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096348700, "dur": 2887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096351588, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096351664, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751312096354312, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096354648, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096355069, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096355173, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751312096355362, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096355456, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096355804, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096355890, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096356150, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751312096356315, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751312096356422, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751312096356579, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751312096356772, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751312096356891, "dur": 649897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096191296, "dur": 28302, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096219610, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220202, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096220282, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220452, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220545, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220608, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220768, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220876, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096220975, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221079, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221163, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221391, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221543, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221652, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096221767, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096221860, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096221913, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096222021, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096222244, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096222416, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223036, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223098, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751312096223228, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223353, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223442, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223672, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096223723, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223887, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096223998, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096224109, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096224307, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096224367, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751312096224490, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096224687, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096224854, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096224935, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096225035, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096225364, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751312096225784, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096226579, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096230404, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751312096228922, "dur": 3441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096232364, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096232933, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096233256, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096233525, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096233922, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096234623, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096234871, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096235136, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096235375, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096235627, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096235941, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096236219, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096236474, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096236777, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096237153, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096237399, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096237688, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096237935, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096238421, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096239105, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096239589, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096239991, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096240290, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096241337, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096242164, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096242484, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096243279, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096243499, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096243574, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096243906, "dur": 1516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096245465, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751312096245546, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096246351, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096246564, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096247005, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096247710, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096247856, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096248011, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096248278, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096248368, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096248648, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096248720, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096249454, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096249514, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096250429, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096250620, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096251005, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096251186, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096251250, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751312096251342, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096252012, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751312096252632, "dur": 265, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096253396, "dur": 72441, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751312096337636, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096340240, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096340341, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096342777, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096342870, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096345410, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096345593, "dur": 2599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096348194, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096348329, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096350690, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096350762, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096350826, "dur": 2777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096353604, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096353746, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751312096356798, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751312096357152, "dur": 649672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751312097017104, "dur": 2315, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 2663, "ts": 1751312097044932, "dur": 9835, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 2663, "ts": 1751312097054818, "dur": 3386, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 2663, "ts": 1751312097037987, "dur": 21576, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}