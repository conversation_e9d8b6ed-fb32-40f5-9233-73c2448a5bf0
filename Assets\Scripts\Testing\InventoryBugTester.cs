using UnityEngine;
using ZombieGame.Inventory;
using ZombieGame.Player;

namespace ZombieGame.Testing
{
    /// <summary>
    /// Test script to verify inventory bug fixes:
    /// 1. Hotbar scroll should not get stuck during item movement
    /// 2. Player movement should be disabled when inventory is open
    /// </summary>
    public class InventoryBugTester : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool enableDebugOutput = true;
        [SerializeField] private bool runTestsOnStart = false;
        
        [Header("References")]
        [SerializeField] private PlayerInventory playerInventory;
        [SerializeField] private PlayerMovement playerMovement;
        [SerializeField] private CameraController cameraController;
        [SerializeField] private PlayerInventoryIntegration inventoryIntegration;
        
        // Test state
        private bool isTestingHotbarScroll = false;
        private int initialHotbarSlot = 0;
        private float testStartTime = 0f;
        
        private void Start()
        {
            // Auto-find components if not assigned
            if (playerInventory == null)
                playerInventory = FindObjectOfType<PlayerInventory>();
            if (playerMovement == null)
                playerMovement = FindObjectOfType<PlayerMovement>();
            if (cameraController == null)
                cameraController = FindObjectOfType<CameraController>();
            if (inventoryIntegration == null)
                inventoryIntegration = FindObjectOfType<PlayerInventoryIntegration>();
            
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllTests());
            }
        }
        
        private void Update()
        {
            // Manual test triggers
            if (Input.GetKeyDown(KeyCode.F1))
            {
                TestPlayerMovementDisabled();
            }
            
            if (Input.GetKeyDown(KeyCode.F2))
            {
                TestHotbarScrolling();
            }
            
            if (Input.GetKeyDown(KeyCode.F3))
            {
                StartCoroutine(RunAllTests());
            }
            
            // Monitor hotbar scroll test
            if (isTestingHotbarScroll)
            {
                MonitorHotbarScrollTest();
            }
        }
        
        /// <summary>
        /// Test that player movement is properly disabled when inventory is open
        /// </summary>
        public void TestPlayerMovementDisabled()
        {
            if (playerInventory == null || playerMovement == null)
            {
                LogError("Missing required components for movement test");
                return;
            }
            
            Log("=== Testing Player Movement Disabled ===");
            
            // Test 1: Check initial state
            bool initialMovementEnabled = playerMovement.enabled;
            bool initialInventoryOpen = playerInventory.IsInventoryOpen;
            Log($"Initial state - Movement enabled: {initialMovementEnabled}, Inventory open: {initialInventoryOpen}");
            
            // Test 2: Open inventory and check movement is disabled
            if (!initialInventoryOpen)
            {
                playerInventory.ToggleInventory();
                Log($"Opened inventory - Movement enabled: {playerMovement.enabled}, Inventory open: {playerInventory.IsInventoryOpen}");
                
                if (playerMovement.enabled)
                {
                    LogError("FAIL: Player movement should be disabled when inventory is open!");
                }
                else
                {
                    Log("PASS: Player movement correctly disabled when inventory is open");
                }
            }
            
            // Test 3: Close inventory and check movement is re-enabled
            if (playerInventory.IsInventoryOpen)
            {
                playerInventory.ToggleInventory();
                Log($"Closed inventory - Movement enabled: {playerMovement.enabled}, Inventory open: {playerInventory.IsInventoryOpen}");
                
                if (!playerMovement.enabled)
                {
                    LogError("FAIL: Player movement should be re-enabled when inventory is closed!");
                }
                else
                {
                    Log("PASS: Player movement correctly re-enabled when inventory is closed");
                }
            }
            
            // Test camera controller as well
            if (cameraController != null)
            {
                Log($"Camera controller enabled: {cameraController.enabled}");
                if (playerInventory.IsInventoryOpen && cameraController.enabled)
                {
                    LogError("FAIL: Camera controller should be disabled when inventory is open!");
                }
                else if (!playerInventory.IsInventoryOpen && !cameraController.enabled)
                {
                    LogError("FAIL: Camera controller should be enabled when inventory is closed!");
                }
                else
                {
                    Log("PASS: Camera controller state is correct");
                }
            }
        }
        
        /// <summary>
        /// Test that hotbar scrolling works properly and doesn't get stuck
        /// </summary>
        public void TestHotbarScrolling()
        {
            if (playerInventory == null)
            {
                LogError("Missing PlayerInventory for hotbar scroll test");
                return;
            }
            
            Log("=== Testing Hotbar Scrolling ===");
            
            // Start hotbar scroll test
            isTestingHotbarScroll = true;
            initialHotbarSlot = playerInventory.SelectedHotbarSlot;
            testStartTime = Time.time;
            
            Log($"Starting hotbar scroll test. Initial slot: {initialHotbarSlot}");
            Log("Try scrolling the mouse wheel to change hotbar slots...");
            Log("Test will automatically end after 10 seconds or press F2 again to stop");
        }
        
        private void MonitorHotbarScrollTest()
        {
            if (Time.time - testStartTime > 10f || Input.GetKeyDown(KeyCode.F2))
            {
                // End test
                isTestingHotbarScroll = false;
                int finalSlot = playerInventory.SelectedHotbarSlot;
                
                Log($"Hotbar scroll test ended. Final slot: {finalSlot}");
                
                if (finalSlot == initialHotbarSlot)
                {
                    LogWarning("WARNING: Hotbar slot didn't change during test. Try scrolling mouse wheel.");
                }
                else
                {
                    Log("PASS: Hotbar scrolling appears to be working");
                }
            }
            else
            {
                // Monitor for slot changes
                int currentSlot = playerInventory.SelectedHotbarSlot;
                if (currentSlot != initialHotbarSlot)
                {
                    Log($"Hotbar slot changed to: {currentSlot}");
                    initialHotbarSlot = currentSlot; // Update for next change
                }
            }
        }
        
        /// <summary>
        /// Run all tests automatically
        /// </summary>
        private System.Collections.IEnumerator RunAllTests()
        {
            Log("=== Running All Inventory Bug Tests ===");
            
            // Test 1: Player movement
            TestPlayerMovementDisabled();
            yield return new WaitForSeconds(1f);
            
            // Test 2: Hotbar scrolling
            Log("Starting hotbar scroll test (5 seconds)...");
            TestHotbarScrolling();
            yield return new WaitForSeconds(5f);
            
            // End hotbar test
            if (isTestingHotbarScroll)
            {
                isTestingHotbarScroll = false;
                Log("Hotbar scroll test completed");
            }
            
            Log("=== All Tests Completed ===");
            Log("Manual test controls:");
            Log("F1 - Test player movement disabled");
            Log("F2 - Test hotbar scrolling");
            Log("F3 - Run all tests");
        }
        
        private void Log(string message)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[InventoryBugTester] {message}");
            }
        }
        
        private void LogWarning(string message)
        {
            if (enableDebugOutput)
            {
                Debug.LogWarning($"[InventoryBugTester] {message}");
            }
        }
        
        private void LogError(string message)
        {
            if (enableDebugOutput)
            {
                Debug.LogError($"[InventoryBugTester] {message}");
            }
        }
    }
}
